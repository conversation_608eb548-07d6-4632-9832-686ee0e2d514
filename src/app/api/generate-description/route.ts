import { NextResponse } from 'next/server';
import OpenA<PERSON> from 'openai';
import {
  SYSTEM_PROMPT_CONTENT_GENERATION,
  SYSTEM_PROMPT_CONTENT_IMPROVEMENT,
  generateUserPrompt,
  generateImprovementPrompt,
  OPENAI_CONFIG,
  type ProductFormData
} from '@/lib/prompts';
import {
  validateSeoLimits,
  validateProductData,
  validateApiOutput,
  type SeoContent
} from '@/lib/seoGenerator';
import {
  generateIntelligentFallback,
  generateImprovementFallback
} from '@/lib/fallbackGenerator';
import {
  openaiErrorHandler,
  classifyOpenAIError,
  generateUserFriendlyErrorMessage,
  logError
} from '@/lib/errorHandler';

const apiKey = process.env.OPENAI_API_KEY;
const fallbackApiKey = process.env.OPENAI_FALLBACK_API_KEY;
const mockMode = process.env.MOCK_OPENAI === 'true';

let openai: OpenAI | null = null;
if (apiKey && !mockMode) {
  openai = new OpenAI({ apiKey });
} else if (fallbackApiKey && !mockMode) {
  openai = new OpenAI({ apiKey: fallbackApiKey });
}

// Manter compatibilidade com interface existente
interface ProductInfo extends ProductFormData {}





// Enhanced mock function using intelligent fallback system
function generateMockSeoContent(productInfo: ProductInfo): SeoContent {
  // Use the intelligent fallback system for consistent mock content
  return generateIntelligentFallback(productInfo);
}













function generateMockImprovedContent(currentDescription: string, productName?: string): SeoContent {
  // Use the intelligent fallback system for consistent mock improvement content
  return generateImprovementFallback(currentDescription, productName);
}

export async function POST(request: Request) {
  // Check if we should use mock mode
  if (mockMode) {
    try {
      const body = await request.json();
      let seoContent: SeoContent;

      if (body.action === 'generate') {
        seoContent = generateMockSeoContent(body.productInfo);
      } else if (body.action === 'improve') {
        seoContent = generateMockImprovedContent(body.currentDescription, body.productInfo?.name);
      } else {
        return NextResponse.json({ error: 'Ação inválida. Use "generate" ou "improve".' }, { status: 400 });
      }

      // Simulate API delay for realistic testing
      await new Promise(resolve => setTimeout(resolve, 1000));

      return NextResponse.json({ seoContent });
    } catch (error) {
      console.error('Erro no modo mock:', error);
      return NextResponse.json({ error: 'Erro no modo de teste.' }, { status: 500 });
    }
  }

  if (!openai) {
    return NextResponse.json(
      { error: 'A API da OpenAI não está configurada. Por favor, defina a variável de ambiente OPENAI_API_KEY ou ative o modo mock.' },
      { status: 500 }
    );
  }

  try {
    const body = await request.json();
    let seoContent: SeoContent;

    if (body.action === 'generate') {
      seoContent = await generateSeoContent(body.productInfo);
    } else if (body.action === 'improve') {
      seoContent = await improveSeoContent(body.currentDescription, body.productInfo?.name);
    } else {
      return NextResponse.json({ error: 'Ação inválida. Use "generate" ou "improve".' }, { status: 400 });
    }

    return NextResponse.json({ seoContent });

  } catch (error) {
    // Log estruturado do erro principal
    logError(error, 'API de descrição de produto', { action: body.action });

    // Classificar erro e gerar resposta apropriada
    const errorClassification = classifyOpenAIError(error);
    const userMessage = generateUserFriendlyErrorMessage(error);

    console.error(`❌ Erro na API de descrição de produto (${errorClassification.category}):`, errorClassification.technicalMessage);

    // Determinar status HTTP baseado na classificação
    let httpStatus = 500;
    switch (errorClassification.category) {
      case 'auth':
        httpStatus = 401;
        break;
      case 'quota':
        httpStatus = 429;
        break;
      case 'client':
        httpStatus = 400;
        break;
      case 'server':
      case 'network':
        httpStatus = 503;
        break;
      default:
        httpStatus = 500;
    }

    return NextResponse.json({
      error: userMessage,
      category: errorClassification.category,
      severity: errorClassification.severity,
      canRetry: errorClassification.shouldRetry
    }, { status: httpStatus });
  }
}

async function generateSeoContent(productInfo: ProductInfo): Promise<SeoContent> {
  if (!openai) throw new Error('API da OpenAI não configurada');

  // Validar dados do produto antes de processar
  const validation = validateProductData(productInfo);
  if (!validation.isValid) {
    throw new Error(`Dados inválidos: ${validation.errors.join(', ')}`);
  }

  // Gerar prompt dinâmico usando a nova estrutura modular
  const userPrompt = generateUserPrompt(productInfo);

  try {
    // Usar sistema de retry inteligente para a chamada da API
    const response = await openaiErrorHandler.executeWithRetry(
      () => openai.chat.completions.create({
        ...OPENAI_CONFIG,
        messages: [
          {
            role: "system",
            content: SYSTEM_PROMPT_CONTENT_GENERATION
          },
          {
            role: "user",
            content: userPrompt
          }
        ]
      }),
      'Geração de conteúdo SEO'
    );

    const content = response.choices[0].message.content;
    if (!content) throw new Error("A resposta da API está vazia.");

    try {
      const rawContent = JSON.parse(content);

      // Usar validação robusta do output da API
      const apiValidation = validateApiOutput(rawContent);

      if (!apiValidation.isValid) {
        console.error("Output da API inválido:", apiValidation.errors);
        throw new Error(`Resposta da API inválida: ${apiValidation.errors.join(', ')}`);
      }

      const parsedContent = apiValidation.correctedContent!;

      // Validar limites de SEO usando a nova função modular
      const seoValidation = validateSeoLimits(parsedContent);
      if (!seoValidation.isValid) {
        console.warn("Limites de SEO não respeitados:", seoValidation.errors);

        // Tentar corrigir automaticamente a meta description
        if (parsedContent.shortDescription.length > 160) {
          let truncated = parsedContent.shortDescription.substring(0, 157);
          const lastSpace = truncated.lastIndexOf(' ');
          if (lastSpace > 140) {
            truncated = truncated.substring(0, lastSpace);
          }
          if (truncated.length < parsedContent.shortDescription.length) {
            truncated += "...";
          }
          if (truncated.length > 160) {
            truncated = truncated.substring(0, 157) + "...";
          }
          parsedContent.shortDescription = truncated;
          console.log(`Meta description corrigida: ${parsedContent.shortDescription.length} caracteres`);
        }
      }

      // Mostrar avisos se existirem
      if (seoValidation.warnings.length > 0) {
        console.warn("Avisos de SEO:", seoValidation.warnings);
      }

      return parsedContent;
    } catch (e) {
      console.error("Falha ao analisar JSON da API:", content);
      throw new Error("A resposta da API não é um JSON válido.");
    }
  } catch (error) {
    // Log estruturado do erro
    logError(error, 'Geração de conteúdo SEO', { productName: productInfo.name });

    // Classificar erro para determinar estratégia
    const errorClassification = classifyOpenAIError(error);

    console.warn(
      `⚠️ API OpenAI falhou (${errorClassification.category}): ${errorClassification.technicalMessage}. ` +
      'Usando fallback inteligente.'
    );

    // Usar fallback inteligente quando a API falha
    const fallbackContent = generateIntelligentFallback(productInfo);

    // Validar o conteúdo do fallback
    const validation = validateSeoLimits(fallbackContent);
    if (!validation.isValid) {
      console.warn("Fallback não atende limites SEO:", validation.errors);
    }

    return fallbackContent;
  }
}

async function improveSeoContent(currentDescription: string, productName?: string): Promise<SeoContent> {
  if (!openai) throw new Error('API da OpenAI não configurada');

  // Validar se a descrição atual não está vazia
  if (!currentDescription || currentDescription.trim().length === 0) {
    throw new Error('Descrição atual é obrigatória para melhoria');
  }

  // Gerar prompt dinâmico para melhoria usando a nova estrutura modular
  const improvementPrompt = generateImprovementPrompt(currentDescription, productName);

  try {
    // Usar sistema de retry inteligente para a chamada da API
    const response = await openaiErrorHandler.executeWithRetry(
      () => openai.chat.completions.create({
        ...OPENAI_CONFIG,
        messages: [
          {
            role: "system",
            content: SYSTEM_PROMPT_CONTENT_IMPROVEMENT
          },
          {
            role: "user",
            content: improvementPrompt
          }
        ]
      }),
      'Melhoria de conteúdo SEO'
    );

    const content = response.choices[0].message.content;
    if (!content) throw new Error("A resposta da API está vazia.");

    try {
      const rawContent = JSON.parse(content);

      // Usar validação robusta do output da API
      const apiValidation = validateApiOutput(rawContent);

      if (!apiValidation.isValid) {
        console.error("Output da API inválido:", apiValidation.errors);
        throw new Error(`Resposta da API inválida: ${apiValidation.errors.join(', ')}`);
      }

      const parsedContent = apiValidation.correctedContent!;

      // Validar limites de SEO usando a nova função modular
      const seoValidation = validateSeoLimits(parsedContent);
      if (!seoValidation.isValid) {
        console.warn("Limites de SEO não respeitados:", seoValidation.errors);

        // Tentar corrigir automaticamente a meta description
        if (parsedContent.shortDescription.length < 140) {
          parsedContent.shortDescription = parsedContent.shortDescription + " Descubra mais detalhes.";
        } else if (parsedContent.shortDescription.length > 160) {
          parsedContent.shortDescription = parsedContent.shortDescription.substring(0, 157) + "...";
        }
      }

      // Mostrar avisos se existirem
      if (seoValidation.warnings.length > 0) {
        console.warn("Avisos de SEO:", seoValidation.warnings);
      }

      return parsedContent;
    } catch (e) {
      console.error("Falha ao analisar JSON da API:", content);
      throw new Error("A resposta da API não é um JSON válido.");
    }
  } catch (error) {
    // Log estruturado do erro
    logError(error, 'Melhoria de conteúdo SEO', { productName });

    // Classificar erro para determinar estratégia
    const errorClassification = classifyOpenAIError(error);

    console.warn(
      `⚠️ API OpenAI falhou na melhoria (${errorClassification.category}): ${errorClassification.technicalMessage}. ` +
      'Usando fallback inteligente.'
    );

    // Usar fallback inteligente para melhoria quando a API falha
    const fallbackContent = generateImprovementFallback(currentDescription, productName);

    // Validar o conteúdo do fallback
    const validation = validateSeoLimits(fallbackContent);
    if (!validation.isValid) {
      console.warn("Fallback de melhoria não atende limites SEO:", validation.errors);
    }

    return fallbackContent;
  }
}
