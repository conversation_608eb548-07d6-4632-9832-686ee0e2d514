import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const apiKey = process.env.OPENAI_API_KEY;
const fallbackApiKey = process.env.OPENAI_FALLBACK_API_KEY;
const mockMode = process.env.MOCK_OPENAI === 'true';

let openai: OpenAI | null = null;
if (apiKey && !mockMode) {
  openai = new OpenAI({ apiKey });
} else if (fallbackApiKey && !mockMode) {
  openai = new OpenAI({ apiKey: fallbackApiKey });
}

interface ProductInfo {
  name: string;
  category?: string;
  features?: string[];
  keywords?: string[];
  targetAudience?: string;
  additionalInfo?: string;
}

interface SeoContent {
  shortDescription: string;
  slug: string;
  wooCommerceMainDescription: string;
  wooCommerceShortDescription: string;
}

// Função para validar limites de caracteres SEO
const validateSeoLimits = (content: SeoContent): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Validar meta description (140-160 caracteres)
  if (content.shortDescription.length < 140 || content.shortDescription.length > 160) {
    errors.push(`Meta description deve ter entre 140-160 caracteres (atual: ${content.shortDescription.length})`);
  }

  // Validar título SEO (50-60 caracteres recomendado para o slug)
  if (content.slug.length > 60) {
    errors.push(`Slug muito longo (atual: ${content.slug.length} caracteres)`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Função para normalizar o slug
const generateSlug = (text: string): string => {
  const a = 'àáâäæãåāăąçćčđďèéêëēėęěğǵḧîïíīįìłḿñńǹňôöòóœøōõőṕŕřßśšşșťțûüùúūǘůűųẃẍÿýžźż·/_,:;';
  const b = 'aaaaaaaaaacccddeeeeeeeegghiiiiiilmnnnnoooooooooprrssssssttuuuuuuuuuwxyyzzz------';
  const p = new RegExp(a.split('').join('|'), 'g');

  return text.toString().toLowerCase()
    .replace(/\s+/g, '-') // Substituir espaços por -
    .replace(p, c => b.charAt(a.indexOf(c))) // Substituir caracteres especiais
    .replace(/&/g, '-and-') // Substituir & por '-and-'
    .replace(/[^\w\-]+/g, '') // Remover caracteres inválidos
    .replace(/\-\-+/g, '-') // Substituir múltiplos - por um único -
    .replace(/^-+/, '') // Remover - do início
    .replace(/-+$/, ''); // Remover - do fim
};

// Gender detection for Portuguese products
function detectProductGender(productName: string): 'masculine' | 'feminine' | 'unknown' {
  if (!productName) return 'unknown';

  const name = productName.toLowerCase().trim();

  // Common feminine products
  const feminineProducts = [
    'camisola', 'blusa', 'saia', 'calça', 'camisa', 'jaqueta', 'jaqueta',
    'mesa', 'cadeira', 'lâmpada', 'máquina', 'televisão', 'impressora',
    'bicicleta', 'mochila', 'carteira', 'bolsa', 'sandália', 'sapatilha',
    'toalha', 'almofada', 'cortina', 'estante', 'prateleira', 'gaveta',
    'panela', 'frigideira', 'chaleira', 'torradeira', 'batedeira',
    'escova', 'pulseira', 'corrente', 'aliança', 'pérola', 'gargantilha'
  ];

  // Common masculine products
  const masculineProducts = [
    'casaco', 'sapato', 'computador', 'telemóvel', 'tablet', 'smartphone',
    'sofá', 'armário', 'relógio', 'anel', 'colar', 'brinco',
    'aspirador', 'secador', 'ferro', 'micro-ondas', 'frigorífico',
    'televisor', 'monitor', 'teclado', 'rato', 'auricular', 'auscultador',
    'carregador', 'cabo', 'adaptador', 'suporte', 'tripé', 'filtro'
  ];

  // Check for exact matches first
  for (const feminine of feminineProducts) {
    if (name.includes(feminine)) return 'feminine';
  }

  for (const masculine of masculineProducts) {
    if (name.includes(masculine)) return 'masculine';
  }

  // Check common endings
  if (name.endsWith('a') || name.endsWith('ção') || name.endsWith('dade') || name.endsWith('gem')) {
    return 'feminine';
  }

  if (name.endsWith('o') || name.endsWith('or') || name.endsWith('ismo')) {
    return 'masculine';
  }

  return 'unknown';
}

// Automatic error correction functions
function standardizeTitle(title: string): string {
  if (!title) return '';

  // Remove extra spaces and normalize
  let standardized = title.trim().replace(/\s+/g, ' ');

  // Apply proper case (capitalize first letter of each word, except articles/prepositions)
  const lowercaseWords = ['de', 'da', 'do', 'das', 'dos', 'e', 'em', 'para', 'com', 'por', 'a', 'o', 'as', 'os'];

  standardized = standardized.toLowerCase().split(' ').map((word, index) => {
    // Always capitalize first word
    if (index === 0) {
      return word.charAt(0).toUpperCase() + word.slice(1);
    }
    // Don't capitalize articles/prepositions unless they're the first word
    if (lowercaseWords.includes(word)) {
      return word;
    }
    // Capitalize other words
    return word.charAt(0).toUpperCase() + word.slice(1);
  }).join(' ');

  // Remove unnecessary punctuation at the end
  standardized = standardized.replace(/[.,;:!?]+$/, '');

  return standardized;
}

function correctSpelling(text: string): string {
  if (!text) return '';

  // Common Portuguese spelling corrections
  const corrections: { [key: string]: string } = {
    // Common typos
    'qualidadde': 'qualidade',
    'funcionalidadde': 'funcionalidade',
    'resistênte': 'resistente',
    'duravél': 'durável',
    'portátil': 'portátil',
    'ergonómico': 'ergonómico',
    'económico': 'económico',
    'tecnológico': 'tecnológico',
    'prático': 'prático',
    'automático': 'automático',
    'electrónico': 'eletrónico',
    'electrónicos': 'eletrónicos',
    'electrónica': 'eletrónica',

    // Consistency in Portuguese variants
    'eletrônico': 'eletrónico',
    'eletrônicos': 'eletrónicos',
    'eletrônica': 'eletrónica',

    // Common word corrections
    'optimizar': 'otimizar',
    'optimizado': 'otimizado',
    'optimização': 'otimização'
  };

  let corrected = text;

  // Apply corrections
  Object.entries(corrections).forEach(([wrong, right]) => {
    const regex = new RegExp(`\\b${wrong}\\b`, 'gi');
    corrected = corrected.replace(regex, right);
  });

  return corrected;
}

function correctGrammarAndFormatting(text: string): string {
  if (!text) return '';

  let corrected = text;

  // Fix spacing issues
  corrected = corrected.replace(/\s+/g, ' '); // Multiple spaces to single space
  corrected = corrected.replace(/\s+([.,;:!?])/g, '$1'); // Remove space before punctuation
  corrected = corrected.replace(/([.,;:!?])([a-zA-Z])/g, '$1 $2'); // Add space after punctuation

  // Fix capitalization after punctuation
  corrected = corrected.replace(/([.!?])\s+([a-z])/g, (match, punct, letter) => {
    return punct + ' ' + letter.toUpperCase();
  });

  // Ensure proper sentence structure
  corrected = corrected.replace(/^\s*([a-z])/g, (match, letter) => {
    return letter.toUpperCase();
  });

  // Fix common grammar patterns
  corrected = corrected.replace(/\bde a\b/g, 'da');
  corrected = corrected.replace(/\bde o\b/g, 'do');
  corrected = corrected.replace(/\bde os\b/g, 'dos');
  corrected = corrected.replace(/\bde as\b/g, 'das');
  corrected = corrected.replace(/\bem o\b/g, 'no');
  corrected = corrected.replace(/\bem a\b/g, 'na');
  corrected = corrected.replace(/\bem os\b/g, 'nos');
  corrected = corrected.replace(/\bem as\b/g, 'nas');

  // Remove extra line breaks and normalize HTML formatting
  if (corrected.includes('<')) {
    corrected = corrected.replace(/>\s+</g, '><'); // Remove spaces between tags
    corrected = corrected.replace(/\n\s*\n\s*\n/g, '\n\n'); // Max two line breaks
  }

  return corrected.trim();
}

// Enhanced mock function following copywriter professional structure
function generateMockSeoContent(productInfo: ProductInfo): SeoContent {
  const productName = standardizeTitle(productInfo.name || 'Produto Exclusivo');
  const category = correctSpelling(productInfo.category || 'categoria premium');
  const features = productInfo.features?.filter(f => f.trim()).map(f => correctSpelling(f.trim())) || [];
  const targetAudience = correctSpelling(productInfo.targetAudience || 'quem procura qualidade e estilo');
  const additionalInfo = correctSpelling(productInfo.additionalInfo || '');

  // Generate professional copywriter-style descriptions
  const professionalDescriptions = generateProfessionalDescriptions(productName, category, features, targetAudience, additionalInfo);

  // Apply automatic error correction to all generated content
  return {
    wooCommerceMainDescription: correctGrammarAndFormatting(professionalDescriptions.mainDescription),
    wooCommerceShortDescription: correctGrammarAndFormatting(professionalDescriptions.shortDescription),
    shortDescription: correctGrammarAndFormatting(professionalDescriptions.seoDescription),
    slug: generateSlug(productName)
  };
}

// Generate professional copywriter-style descriptions following the 4-paragraph structure
function generateProfessionalDescriptions(productName: string, category: string, features: string[], targetAudience: string, additionalInfo: string) {
  // 1. INTRODUÇÃO - Destaca o principal benefício ou diferencial
  const introductions = [
    `O ${productName} representa a escolha ideal para ${targetAudience} que procuram funcionalidade e design numa única solução.`,
    `Desenvolvido especificamente para ${targetAudience}, o ${productName} combina inovação e praticidade de forma excecional.`,
    `Para ${targetAudience} que valorizam qualidade e eficiência, o ${productName} oferece exatamente o que procuram.`,
    `O ${productName} destaca-se como a solução perfeita para ${targetAudience} que não abdicam da excelência.`,
    `Pensado para ${targetAudience} exigentes, o ${productName} redefine os padrões de qualidade e desempenho.`
  ];

  // 2. CARACTERÍSTICAS TÉCNICAS - Apresenta características de forma natural e fluida
  const technicalDescriptions = generateTechnicalParagraph(features, additionalInfo, category);

  // 3. UTILIZAÇÃO PRÁTICA - Explica cenários reais de uso
  const practicalUsages = [
    `Na prática, este produto integra-se perfeitamente na rotina diária, simplificando tarefas e melhorando a experiência de utilização.`,
    `O seu design versátil permite uma adaptação natural a diferentes contextos, desde ambientes profissionais a ocasiões mais casuais.`,
    `A sua funcionalidade prática torna-o indispensável para quem procura eficiência e comodidade no dia a dia.`,
    `Adequa-se a diversas situações de uso, oferecendo sempre a flexibilidade e o desempenho que precisa.`,
    `A sua versatilidade permite uma utilização intuitiva, adaptando-se às necessidades específicas de cada momento.`
  ];

  // 4. CHAMADA À AÇÃO SUAVE - Convite discreto e amigável
  const softCallToActions = [
    `Descubra como o ${productName} pode transformar a sua experiência e elevar o seu padrão de qualidade.`,
    `Experimente a diferença que um produto verdadeiramente bem concebido pode fazer na sua vida.`,
    `Invista numa solução que acompanhará as suas necessidades e superará as suas expectativas.`,
    `Adicione o ${productName} à sua rotina e sinta imediatamente a melhoria na qualidade e eficiência.`,
    `Permita-se descobrir um novo nível de satisfação com um produto que realmente faz a diferença.`
  ];

  // Selecionar elementos aleatoriamente para variedade
  const introduction = introductions[Math.floor(Math.random() * introductions.length)];
  const practicalUsage = practicalUsages[Math.floor(Math.random() * practicalUsages.length)];
  const callToAction = softCallToActions[Math.floor(Math.random() * softCallToActions.length)];

  // Construir descrição principal seguindo a estrutura de 4 parágrafos
  const mainDescription = `<p>${introduction}</p>

<p>${technicalDescriptions}</p>

<p>${practicalUsage}</p>

<p>${callToAction}</p>`;

  // Gerar descrição curta (texto corrido, específica)
  const shortDescription = generateShortDescription(productName, category, features, targetAudience);

  // Gerar descrição SEO (máximo 160 caracteres)
  const seoDescription = generateOptimalSeoDescription(productName, category, features, targetAudience);

  return {
    mainDescription,
    shortDescription,
    seoDescription
  };
}

// Helper function to generate technical paragraph with natural flow
function generateTechnicalParagraph(features: string[], additionalInfo: string, category: string): string {
  let techParagraph = '';

  if (features.length > 0) {
    // Create natural sentences from features
    const enhancedFeatures = features.map(feature => enhanceFeatureNaturally(feature.trim(), category));

    if (enhancedFeatures.length === 1) {
      techParagraph = `Destaca-se pela ${enhancedFeatures[0]}.`;
    } else if (enhancedFeatures.length === 2) {
      techParagraph = `Combina ${enhancedFeatures[0]} com ${enhancedFeatures[1]}.`;
    } else {
      const lastFeature = enhancedFeatures.pop();
      techParagraph = `Integra ${enhancedFeatures.join(', ')} e ${lastFeature}.`;
    }
  }

  if (additionalInfo) {
    techParagraph += ` ${additionalInfo}`;
  }

  // Add context based on category if no specific features
  if (!techParagraph) {
    const categoryContexts: { [key: string]: string } = {
      'eletrónica': 'Incorpora tecnologia avançada e design moderno para uma experiência de utilização superior.',
      'mobiliário': 'Apresenta linhas elegantes e acabamentos cuidados que valorizam qualquer ambiente.',
      'roupa': 'Combina tecidos de qualidade com um corte moderno e confortável.',
      'casa': 'Oferece funcionalidade prática com uma estética cuidada e durabilidade.',
      'jardim': 'Construído com materiais resistentes e design prático para uso exterior.',
      'desporto': 'Desenvolvido com materiais técnicos e design ergonómico para máximo desempenho.',
      'beleza': 'Formulado com ingredientes cuidadosamente selecionados para resultados eficazes.',
      'automóvel': 'Fabricado com materiais robustos e acabamento profissional para durabilidade.'
    };

    techParagraph = categoryContexts[category.toLowerCase()] || 'Apresenta características técnicas cuidadosamente desenvolvidas para garantir qualidade e funcionalidade.';
  }

  return techParagraph;
}

// Helper function to enhance features naturally based on category
function enhanceFeatureNaturally(feature: string, category: string): string {
  const lowerFeature = feature.toLowerCase();

  // Category-specific enhancements
  const categoryEnhancements: { [key: string]: { [key: string]: string } } = {
    'eletrónica': {
      'resistente': 'construção robusta e durável',
      'leve': 'design compacto e leve',
      'rápido': 'processamento rápido e eficiente',
      'bateria': 'autonomia de bateria prolongada'
    },
    'mobiliário': {
      'resistente': 'estrutura sólida e resistente',
      'leve': 'design elegante e funcional',
      'madeira': 'madeira de qualidade superior',
      'metal': 'acabamentos metálicos premium'
    },
    'roupa': {
      'confortável': 'conforto excecional',
      'respirável': 'tecidos respiráveis',
      'resistente': 'materiais duráveis',
      'elástico': 'flexibilidade e liberdade de movimento'
    }
  };

  const categorySpecific = categoryEnhancements[category.toLowerCase()];
  if (categorySpecific) {
    for (const [key, enhancement] of Object.entries(categorySpecific)) {
      if (lowerFeature.includes(key)) {
        return enhancement;
      }
    }
  }

  // Generic enhancements
  if (lowerFeature.includes('resistente')) return 'resistência e durabilidade excepcionais';
  if (lowerFeature.includes('leve')) return 'leveza e praticidade';
  if (lowerFeature.includes('confortável')) return 'conforto superior';
  if (lowerFeature.includes('fácil')) return 'facilidade de utilização';
  if (lowerFeature.includes('prático')) return 'funcionalidade prática';

  return `${feature} de qualidade`;
}

// Helper function to generate short description (texto corrido)
function generateShortDescription(productName: string, category: string, features: string[], targetAudience: string): string {
  const mainBenefit = features.length > 0 ? features[0] : 'qualidade superior';
  const secondaryBenefit = features.length > 1 ? features[1] : '';

  if (secondaryBenefit) {
    return `${productName} com ${mainBenefit} e ${secondaryBenefit}, ideal para ${targetAudience} que procuram funcionalidade e design numa única solução.`;
  } else {
    return `${productName} com ${mainBenefit}, desenvolvido especificamente para ${targetAudience} que valorizam qualidade e praticidade.`;
  }
}





// Generate SEO-optimized description (140-160 characters)
function generateOptimalSeoDescription(productName: string, category: string, features: string[], targetAudience: string): string {
  const baseDescription = `${productName} ${category}`;
  let seoDesc = baseDescription;

  // Add key features if space allows
  if (features.length > 0) {
    const keyFeature = features[0].toLowerCase();
    seoDesc += ` com ${keyFeature}`;
  }

  // Add target audience
  seoDesc += ` para ${targetAudience}`;

  // Add compelling ending
  const endings = [
    '. Qualidade premium garantida',
    '. Entrega rápida em Portugal',
    '. Melhor preço e qualidade',
    '. Compre já com garantia',
    '. Produto de confiança'
  ];

  // Choose ending that fits within 160 characters
  for (const ending of endings) {
    if ((seoDesc + ending).length <= 160 && (seoDesc + ending).length >= 140) {
      return seoDesc + ending;
    }
  }

  // If still too short, add more descriptive content
  if (seoDesc.length < 140) {
    seoDesc += '. Produto de qualidade superior com garantia de satisfação total';
  }

  // Ensure it's within limits
  if (seoDesc.length > 160) {
    seoDesc = seoDesc.substring(0, 157) + '...';
  }

  return seoDesc;
}

function generateMockImprovedContent(currentDescription: string, productName?: string): SeoContent {
  const name = standardizeTitle(productName || 'Produto Otimizado');
  const cleanDesc = correctGrammarAndFormatting(currentDescription.replace(/<[^>]*>/g, '').trim());

  // Extract key information from current description
  const words = cleanDesc.split(' ').filter(word => word.length > 3);
  const keyWords = words.slice(0, 5).join(' ');

  // Generate improved content with natural flow
  const improvements = [
    'design renovado e mais atrativo',
    'funcionalidades aprimoradas para melhor desempenho',
    'materiais de qualidade superior',
    'tecnologia mais avançada',
    'maior durabilidade e resistência',
    'melhor experiência do utilizador',
    'características otimizadas'
  ];

  const selectedImprovements = improvements.slice(0, 3);

  const improvedMainDescription = `<div class="improved-product">
    <h2>🚀 ${name} - Versão Melhorada</h2>

    <div class="original-content">
      <h3>📝 Descrição Original</h3>
      <p>${cleanDesc}</p>
    </div>

    <div class="improvements">
      <h3>⚡ Melhorias Implementadas</h3>
      <p>Esta versão otimizada do <strong>${name}</strong> incorpora várias melhorias significativas que elevam a qualidade e funcionalidade do produto:</p>

      <ul class="improvement-list">
        ${selectedImprovements.map(improvement => `<li>🔧 ${improvement.charAt(0).toUpperCase() + improvement.slice(1)}</li>`).join('')}
      </ul>

      <p>Estas melhorias foram desenvolvidas com base no feedback dos utilizadores e nas mais recentes inovações tecnológicas, garantindo um produto que supera as expectativas.</p>
    </div>

    <div class="benefits">
      <h3>🎯 Vantagens da Versão Melhorada</h3>
      <p>Ao escolher esta versão otimizada, beneficia de um produto que combina a confiabilidade comprovada com as mais recentes inovações. Uma escolha inteligente para quem procura o melhor em qualidade e desempenho.</p>

      <div class="guarantee-section">
        <p><strong>✨ Qualidade Aprimorada:</strong> Todas as melhorias foram testadas e validadas.</p>
        <p><strong>🔄 Compatibilidade:</strong> Mantém todas as funcionalidades da versão anterior.</p>
        <p><strong>📈 Performance:</strong> Desempenho superior em todos os aspetos.</p>
      </div>
    </div>
  </div>`;

  const improvedShortDescription = `${name} - versão melhorada com ${selectedImprovements[0]} e ${selectedImprovements[1]}. Qualidade superior garantida.`;

  const improvedSeoDescription = generateOptimalSeoDescription(
    name,
    'versão melhorada',
    ['otimizado', 'melhorado'],
    'quem procura qualidade superior'
  );

  return {
    wooCommerceMainDescription: correctGrammarAndFormatting(improvedMainDescription),
    wooCommerceShortDescription: correctGrammarAndFormatting(improvedShortDescription),
    shortDescription: correctGrammarAndFormatting(improvedSeoDescription),
    slug: generateSlug(name)
  };
}

export async function POST(request: Request) {
  // Check if we should use mock mode
  if (mockMode) {
    try {
      const body = await request.json();
      let seoContent: SeoContent;

      if (body.action === 'generate') {
        seoContent = generateMockSeoContent(body.productInfo);
      } else if (body.action === 'improve') {
        seoContent = generateMockImprovedContent(body.currentDescription, body.productInfo?.name);
      } else {
        return NextResponse.json({ error: 'Ação inválida. Use "generate" ou "improve".' }, { status: 400 });
      }

      // Simulate API delay for realistic testing
      await new Promise(resolve => setTimeout(resolve, 1000));

      return NextResponse.json({ seoContent });
    } catch (error) {
      console.error('Erro no modo mock:', error);
      return NextResponse.json({ error: 'Erro no modo de teste.' }, { status: 500 });
    }
  }

  if (!openai) {
    return NextResponse.json(
      { error: 'A API da OpenAI não está configurada. Por favor, defina a variável de ambiente OPENAI_API_KEY ou ative o modo mock.' },
      { status: 500 }
    );
  }

  try {
    const body = await request.json();
    let seoContent: SeoContent;

    if (body.action === 'generate') {
      seoContent = await generateSeoContent(body.productInfo);
    } else if (body.action === 'improve') {
      seoContent = await improveSeoContent(body.currentDescription, body.productInfo?.name);
    } else {
      return NextResponse.json({ error: 'Ação inválida. Use "generate" ou "improve".' }, { status: 400 });
    }

    return NextResponse.json({ seoContent });

  } catch (error) {
    console.error('Erro na API de descrição de produto:', error);

    // Handle specific OpenAI API errors
    if (error && typeof error === 'object' && 'status' in error) {
      const openaiError = error as any;

      switch (openaiError.status) {
        case 429:
          return NextResponse.json({
            error: 'Quota da API OpenAI excedida. Por favor, verifique o seu plano e detalhes de faturação.'
          }, { status: 429 });

        case 401:
          return NextResponse.json({
            error: 'Chave da API OpenAI inválida. Por favor, verifique a configuração.'
          }, { status: 401 });

        case 400:
          return NextResponse.json({
            error: 'Pedido inválido enviado para a API OpenAI. Por favor, tente novamente.'
          }, { status: 400 });

        case 503:
          return NextResponse.json({
            error: 'Serviço OpenAI temporariamente indisponível. Tente novamente em alguns minutos.'
          }, { status: 503 });

        default:
          return NextResponse.json({
            error: `Erro da API OpenAI (${openaiError.status}): ${openaiError.message || 'Erro desconhecido'}`
          }, { status: openaiError.status || 500 });
      }
    }

    // Handle other types of errors
    if (error instanceof Error) {
      if (error.message.includes('API da OpenAI não configurada')) {
        return NextResponse.json({
          error: 'API da OpenAI não está configurada. Por favor, defina a variável de ambiente OPENAI_API_KEY.'
        }, { status: 500 });
      }

      if (error.message.includes('JSON válido')) {
        return NextResponse.json({
          error: 'Resposta inválida da API OpenAI. Tente novamente.'
        }, { status: 500 });
      }

      return NextResponse.json({
        error: `Erro: ${error.message}`
      }, { status: 500 });
    }

    // Fallback for unknown errors
    return NextResponse.json({
      error: 'Falha ao processar o pedido. Tente novamente.'
    }, { status: 500 });
  }
}

async function generateSeoContent(productInfo: ProductInfo): Promise<SeoContent> {
  if (!openai) throw new Error('API da OpenAI não configurada');

  const prompt = `
    Atua como um redator profissional especializado em e-commerce e SEO, com foco em português de Portugal.

    O teu objetivo é gerar automaticamente descrições de produto com base nas informações preenchidas por utilizadores num formulário. Essas informações podem conter erros gramaticais, palavras incompletas, nomes genéricos ou mal escritos — a tua tarefa é **interpretar corretamente o que foi escrito, corrigir automaticamente e gerar textos profissionais de alta qualidade**.

    DADOS DO PRODUTO:
    - Nome do Produto: ${productInfo.name}
    ${productInfo.category ? `- Categoria: ${productInfo.category}` : ''}
    ${productInfo.features && productInfo.features.length > 0 ? `- Características Principais: ${productInfo.features.join(', ')}` : ''}
    ${productInfo.keywords && productInfo.keywords.length > 0 ? `- Palavras-chave SEO: ${productInfo.keywords.join(', ')}` : ''}
    ${productInfo.targetAudience ? `- Público-alvo: ${productInfo.targetAudience}` : ''}
    ${productInfo.additionalInfo ? `- Informações Adicionais: ${productInfo.additionalInfo}` : ''}

    ### 🎯 Regras principais:

    1. **Corrige erros ortográficos, gramaticais e de concordância** com base no português de Portugal. Nunca uses português do Brasil.

    2. **Identifica o tipo de produto**, mesmo com nomes incompletos ou com erros, usando o campo "Nome do Produto", "Categoria" e "Características Principais".

    3. **Adapta a linguagem ao público-alvo**, se esse campo estiver preenchido (ex: técnicos, desportistas, crianças, empresas, etc.).

    4. Gera os seguintes textos com base nos dados do formulário:
       - **Descrição WooCommerce** (3 a 5 parágrafos): descrição envolvente, informativa e com foco nos diferenciais do produto.
       - **Curta Descrição WooCommerce** (máx. 1 frase): resumo direto com os principais benefícios.
       - **Descrição SEO** (até 160 caracteres): otimizada para motores de busca, clara e com palavra-chave principal incluída naturalmente.
       - **Slug SEO-friendly**: gerado automaticamente a partir do nome do produto, usando hífens e sem caracteres especiais.

    5. **Incorpora as palavras-chave SEO** fornecidas no texto de forma natural, sem forçar. Dá prioridade à primeira palavra-chave na Descrição SEO.

    6. **Transforma as características principais em benefícios claros e tangíveis**:
       - Exemplo: "impermeável" ➝ "protege da chuva e humidade, ideal para dias instáveis"
       - Exemplo: "sem fios" ➝ "liberdade total de movimento, sem cabos a atrapalhar"

    7. **Se o utilizador preencher o campo 'Informações Adicionais'**, incorpora essas informações como reforço técnico, diferenciação ou exemplo prático.

    8. **Nunca inventes funcionalidades nem exageres**. Sê realista, claro e persuasivo.

    9. Evita repetições, frases genéricas e expressões sem valor. Dá prioridade a:
       - Clareza
       - Estrutura lógica
       - Escrita fluida
       - Frases curtas com impacto

    10. A escrita deve ser humanizada, natural e adequada ao produto. Usa uma abordagem comercial, mas com elegância.

    ### 📤 Formato de saída esperado:

    Gera sempre os textos com base nestes princípios, adaptando-os ao tipo de produto e linguagem esperada para o seu público. O conteúdo final deve estar pronto para ser publicado num e-commerce com WooCommerce, com qualidade editorial, correção gramatical impecável e otimização SEO.

    **IMPORTANTE:** Antes de gerar qualquer texto, identifica corretamente o género gramatical do produto (masculino/feminino) e garante que TODOS os artigos, adjetivos, particípios e pronomes concordam corretamente. Por exemplo:
    - ❌ "o Camisola em Bico" → ✅ "a Camisola em Bico"
    - ❌ "desenvolvido especificamente" (para produto feminino) → ✅ "desenvolvida especificamente"
    - ❌ "este máquina" → ✅ "esta máquina"

    Campos a gerar:
    1. **wooCommerceMainDescription**: Descrição WooCommerce (3-5 parágrafos em HTML com tags <p>)
    2. **wooCommerceShortDescription**: Curta Descrição WooCommerce (máximo 1 frase, texto corrido)
    3. **shortDescription**: Descrição SEO (até 160 caracteres, otimizada para motores de busca)
    4. **slug**: Slug SEO-friendly (hífens, sem caracteres especiais)

    Responde APENAS com o objeto JSON:
    {
      "wooCommerceMainDescription": "(texto aqui)",
      "wooCommerceShortDescription": "(texto aqui)",
      "shortDescription": "(texto aqui)",
      "slug": "(slug aqui)"
    }
  `;

  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: "És um redator profissional especializado em e-commerce e SEO para o mercado português. Corriges automaticamente erros gramaticais, ortográficos e de concordância. Identificas o género dos produtos e garantis concordância total de artigos, adjetivos e particípios. Escreves sempre em português europeu correto, com tom natural e comercial. A resposta deve ser sempre um objeto JSON válido."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.6,
      max_tokens: 1200,
      response_format: { type: "json_object" },
    });

    const content = response.choices[0].message.content;
    if (!content) throw new Error("A resposta da API está vazia.");

    try {
      const parsedContent: SeoContent = JSON.parse(content);

      // Garante que o slug está bem formatado, mesmo que a IA falhe
      parsedContent.slug = generateSlug(parsedContent.slug || productInfo.name);

      // Validar limites de SEO
      const validation = validateSeoLimits(parsedContent);
      if (!validation.isValid) {
        console.warn("Limites de SEO não respeitados:", validation.errors);

        // Tentar corrigir a meta description se estiver fora dos limites
        if (parsedContent.shortDescription.length > 160) {
          // Se muito longa, truncar de forma inteligente
          let truncated = parsedContent.shortDescription.substring(0, 157);

          // Tentar cortar na última palavra completa
          const lastSpace = truncated.lastIndexOf(' ');
          if (lastSpace > 140) {
            truncated = truncated.substring(0, lastSpace);
          }

          // Adicionar reticências se necessário
          if (truncated.length < parsedContent.shortDescription.length) {
            truncated += "...";
          }

          // Garantir que não excede 160 caracteres
          if (truncated.length > 160) {
            truncated = truncated.substring(0, 157) + "...";
          }

          parsedContent.shortDescription = truncated;
          console.log(`Meta description corrigida: ${parsedContent.shortDescription.length} caracteres`);
        }
      }

      return parsedContent;
    } catch (e) {
      console.error("Falha ao analisar JSON da API:", content);
      throw new Error("A resposta da API não é um JSON válido.");
    }
  } catch (error) {
    // Re-throw OpenAI API errors to be handled by the main catch block
    throw error;
  }
}

async function improveSeoContent(currentDescription: string, productName?: string): Promise<SeoContent> {
  if (!openai) throw new Error('API da OpenAI não configurada');

  const prompt = `
    Atua como um redator profissional especializado em e-commerce e SEO, com foco em português de Portugal.

    A tua tarefa é **melhorar e reescrever uma descrição existente**, corrigindo automaticamente todos os erros gramaticais, ortográficos e de concordância, e transformando-a num texto profissional de alta qualidade.

    DADOS DISPONÍVEIS:
    - Nome do Produto: ${productName || 'Não fornecido'}
    - Descrição Atual: "${currentDescription}"

    ### 🎯 Regras principais para melhoria:

    1. **Corrige TODOS os erros ortográficos, gramaticais e de concordância** com base no português de Portugal. Nunca uses português do Brasil.

    2. **Identifica e corrige erros de género gramatical**:
       - ❌ "o camisola" → ✅ "a camisola"
       - ❌ "desenvolvido especificamente" (para produto feminino) → ✅ "desenvolvida especificamente"
       - ❌ "este máquina" → ✅ "esta máquina"
       - ❌ "um televisão" → ✅ "uma televisão"

    3. **Melhora a estrutura e fluidez** do texto, mantendo as informações originais mas tornando-as mais claras e persuasivas.

    4. **Transforma características em benefícios tangíveis** quando possível.

    5. **Mantém o foco comercial** mas com elegância e naturalidade.

    6. **Evita repetições e frases genéricas**, priorizando:
       - Clareza
       - Estrutura lógica
       - Escrita fluida
       - Frases curtas com impacto

    7. **Nunca inventes funcionalidades** que não estejam na descrição original.

    8. A escrita deve ser humanizada, natural e adequada ao produto.

    ### 📤 Formato de saída esperado:

    Gera sempre os textos melhorados com base nestes princípios, mantendo as informações originais mas corrigindo todos os erros e melhorando a qualidade editorial. O conteúdo final deve estar pronto para ser publicado num e-commerce com WooCommerce, com correção gramatical impecável e otimização SEO.

    **IMPORTANTE:** Antes de gerar qualquer texto, identifica corretamente o género gramatical do produto (masculino/feminino) e corrige TODOS os artigos, adjetivos, particípios e pronomes. Por exemplo:
    - ❌ "o Camisola em Bico" → ✅ "a Camisola em Bico"
    - ❌ "desenvolvido especificamente" (para produto feminino) → ✅ "desenvolvida especificamente"
    - ❌ "este máquina" → ✅ "esta máquina"

    Campos a gerar:
    1. **wooCommerceMainDescription**: Descrição WooCommerce melhorada (3-5 parágrafos em HTML com tags <p>)
    2. **wooCommerceShortDescription**: Curta Descrição WooCommerce melhorada (máximo 1 frase, texto corrido)
    3. **shortDescription**: Descrição SEO otimizada (até 160 caracteres, otimizada para motores de busca)
    4. **slug**: Slug SEO-friendly melhorado (hífens, sem caracteres especiais)

    Responde APENAS com o objeto JSON:
    {
      "wooCommerceMainDescription": "(texto aqui)",
      "wooCommerceShortDescription": "(texto aqui)",
      "shortDescription": "(texto aqui)",
      "slug": "(slug aqui)"
    }
  `;

  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: "És um copywriter profissional especializado em melhorar conteúdo de e-commerce para o mercado português com expertise em correção gramatical automática. PRIMEIRO identificas e corriges TODOS os erros de concordância de género na descrição existente (artigos, adjetivos, particípios, pronomes). Analisas descrições existentes e reescreves-nas com raciocínio inteligente, melhorando a linguagem, estrutura e persuasão. Escreves sempre em português europeu correto, com tom natural e empático. Validates cada frase para concordância de género antes de finalizar. A resposta deve ser sempre um objeto JSON válido."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.6,
      max_tokens: 1200,
      response_format: { type: "json_object" },
    });

    const content = response.choices[0].message.content;
    if (!content) throw new Error("A resposta da API está vazia.");

    try {
      const parsedContent: SeoContent = JSON.parse(content);

      // Garante que o slug está bem formatado
      parsedContent.slug = generateSlug(parsedContent.slug || productName || currentDescription.substring(0, 50));

      // Validar limites de SEO
      const validation = validateSeoLimits(parsedContent);
      if (!validation.isValid) {
        console.warn("Limites de SEO não respeitados:", validation.errors);

        // Tentar corrigir a meta description se estiver fora dos limites
        if (parsedContent.shortDescription.length < 140) {
          // Se muito curta, expandir ligeiramente
          parsedContent.shortDescription = parsedContent.shortDescription + " Descubra mais detalhes.";
        } else if (parsedContent.shortDescription.length > 160) {
          // Se muito longa, truncar mantendo sentido
          parsedContent.shortDescription = parsedContent.shortDescription.substring(0, 157) + "...";
        }
      }

      return parsedContent;
    } catch (e) {
      console.error("Falha ao analisar JSON da API:", content);
      throw new Error("A resposta da API não é um JSON válido.");
    }
  } catch (error) {
    // Re-throw OpenAI API errors to be handled by the main catch block
    throw error;
  }
}
